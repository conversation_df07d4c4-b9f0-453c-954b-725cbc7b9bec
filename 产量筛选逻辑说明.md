# 产量筛选逻辑说明

## 概述

根据您的要求，我已经实现了明确的日期、白班、夜班产量筛选逻辑。核心原则是：**今天早上8点到明天早上7:59这个期间都用同一个productdate去筛选**。

## 时间范围定义

### 班次时间划分
- **白班(B)**: 当天8:00-19:59
- **夜班(Y)**: 当天20:00-次日7:59

### 数据归属原则
- 使用 `productdate` 字段作为主要筛选条件
- 使用 `workclass` 字段区分班次（B=白班，Y=夜班）
- 时间范围：今天早上8:00到明天早上7:59都归属于同一个productdate

## 数据库字段说明

### 主要字段
```sql
productdate    -- 产品日期 (YYYY-MM-DD格式)
workclass      -- 班次 (B=白班, Y=夜班)
productcount   -- 产量
createtime     -- 创建时间
devno          -- 设备编号
```

### 示例数据
根据您提供的数据表格：
```
productdate: 2025-07-29
workclass: Y (夜班)
productcount: 1632
```

## 新增功能

### 1. 核心筛选函数
```python
def get_production_data_by_date_and_shift(productdate, workclass_filter=None):
    """
    根据产品日期和班次筛选产量数据
    
    参数:
    - productdate: 产品日期 (YYYY-MM-DD格式)
    - workclass_filter: 班次过滤 ('B'=白班, 'Y'=夜班, None=全部)
    """
```

### 2. 新增API端点
```
GET /api/production-by-date-shift
```

#### 查询参数
- `productdate`: 产品日期 (YYYY-MM-DD格式，默认今天)
- `workclass`: 班次 (B=白班, Y=夜班, 不传=全部)

#### 示例请求
```
# 查询7月29日白班产量
GET /api/production-by-date-shift?productdate=2025-07-29&workclass=B

# 查询7月29日夜班产量
GET /api/production-by-date-shift?productdate=2025-07-29&workclass=Y

# 查询7月29日全部班次产量
GET /api/production-by-date-shift?productdate=2025-07-29
```

### 3. 测试页面
访问地址：`http://localhost:5000/production-filter-test`

功能特点：
- 可视化筛选界面
- 实时查询结果展示
- 详细的筛选逻辑说明
- 汇总统计信息

## SQL查询逻辑

### 基础查询
```sql
SELECT 
    p.devno,
    d.devname,
    p.workclass,
    p.productcount,
    p.createtime
FROM devproductcount p
LEFT JOIN devinfotable d ON p.devno = d.devno
WHERE p.productdate = '2025-07-29'  -- 使用productdate筛选
ORDER BY p.devno, p.workclass, p.createtime
```

### 按班次筛选
```sql
-- 只查询白班数据
WHERE p.productdate = '2025-07-29' AND p.workclass = 'B'

-- 只查询夜班数据
WHERE p.productdate = '2025-07-29' AND p.workclass = 'Y'
```

### 统计查询
```sql
SELECT
    p.devno,
    d.devname,
    SUM(CASE WHEN p.workclass = 'B' THEN CAST(p.productcount AS INT) ELSE 0 END) as day_shift_count,
    SUM(CASE WHEN p.workclass = 'Y' THEN CAST(p.productcount AS INT) ELSE 0 END) as night_shift_count
FROM devproductcount p
LEFT JOIN devinfotable d ON p.devno = d.devno
WHERE p.productdate = '2025-07-29'
GROUP BY p.devno, d.devname
```

## 返回数据格式

### API响应结构
```json
{
    "devices": [
        {
            "devno": "182",
            "devname": "设备182",
            "dayShift": 1632,      // 白班产量
            "nightShift": 354,     // 夜班产量
            "total": 1986,         // 总产量
            "records": [...]       // 详细记录
        }
    ],
    "summary": {
        "totalDayShift": 5000,     // 所有设备白班总产量
        "totalNightShift": 2000,   // 所有设备夜班总产量
        "totalProduction": 7000,   // 总产量
        "deviceCount": 10,         // 设备数量
        "productdate": "2025-07-29",
        "workclass_filter": "B"    // 筛选的班次
    },
    "explanation": {
        "time_range": "今天早上8点到明天早上7:59这个期间都用2025-07-29去筛选",
        "shift_definition": {
            "B": "白班 - 当天8:00-19:59",
            "Y": "夜班 - 当天20:00-次日7:59"
        },
        "query_info": {
            "productdate": "2025-07-29",
            "workclass_filter": "B",
            "sql_condition": "productdate = '2025-07-29' AND workclass = 'B'"
        }
    }
}
```

## 使用示例

### 1. 查询今天的白班产量
```javascript
fetch('/api/production-by-date-shift?workclass=B')
    .then(response => response.json())
    .then(data => {
        console.log('白班总产量:', data.summary.totalDayShift);
        console.log('设备详情:', data.devices);
    });
```

### 2. 查询指定日期的夜班产量
```javascript
fetch('/api/production-by-date-shift?productdate=2025-07-29&workclass=Y')
    .then(response => response.json())
    .then(data => {
        console.log('夜班总产量:', data.summary.totalNightShift);
    });
```

### 3. 查询指定日期的全部产量
```javascript
fetch('/api/production-by-date-shift?productdate=2025-07-29')
    .then(response => response.json())
    .then(data => {
        console.log('白班产量:', data.summary.totalDayShift);
        console.log('夜班产量:', data.summary.totalNightShift);
        console.log('总产量:', data.summary.totalProduction);
    });
```

## 核心优势

1. **明确的时间范围**: 严格按照productdate字段筛选，避免时间混乱
2. **班次清晰划分**: B和Y班次明确区分，便于统计分析
3. **灵活的查询方式**: 支持按日期、班次、设备等多维度筛选
4. **完整的数据返回**: 包含汇总统计和详细记录
5. **易于理解**: 提供详细的查询说明和时间范围解释

## 测试建议

1. 访问测试页面：`http://localhost:5000/production-filter-test`
2. 选择不同的日期和班次进行测试
3. 观察返回的数据结构和统计信息
4. 验证筛选逻辑是否符合预期

这个新的筛选逻辑完全按照您的要求实现：**使用productdate去筛选，今天早上八点到明天早上7.59这个期间都用这个日期去筛选，workclass B和Y分别对应白班和夜班**。
