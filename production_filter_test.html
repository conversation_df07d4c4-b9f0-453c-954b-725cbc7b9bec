<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产量筛选测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .filter-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        input, select, button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin-left: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .results-section {
            margin-top: 20px;
        }
        .summary-card {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .device-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .device-table th, .device-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .device-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .device-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .explanation {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>产量筛选测试页面</h1>
        
        <div class="explanation">
            <h3>筛选逻辑说明：</h3>
            <ul>
                <li><strong>时间范围：</strong>今天早上8点到明天早上7:59这个期间都用同一个productdate去筛选</li>
                <li><strong>白班(B)：</strong>当天8:00-19:59</li>
                <li><strong>夜班(Y)：</strong>当天20:00-次日7:59</li>
                <li><strong>筛选条件：</strong>使用productdate字段进行日期筛选，workclass字段进行班次筛选</li>
            </ul>
        </div>

        <div class="filter-section">
            <h3>筛选条件</h3>
            <div class="form-group">
                <label for="productdate">产品日期：</label>
                <input type="date" id="productdate" value="">
            </div>
            <div class="form-group">
                <label for="workclass">班次：</label>
                <select id="workclass">
                    <option value="">全部班次</option>
                    <option value="B">白班(B)</option>
                    <option value="Y">夜班(Y)</option>
                </select>
            </div>
            <div class="form-group">
                <button onclick="queryProduction()">查询产量</button>
                <button onclick="clearResults()">清空结果</button>
            </div>
        </div>

        <div class="results-section" id="results" style="display: none;">
            <h3>查询结果</h3>
            <div id="summary"></div>
            <div id="devices"></div>
            <div id="query-info"></div>
        </div>
    </div>

    <script>
        // 设置默认日期为今天
        document.getElementById('productdate').value = new Date().toISOString().split('T')[0];

        async function queryProduction() {
            const productdate = document.getElementById('productdate').value;
            const workclass = document.getElementById('workclass').value;
            
            if (!productdate) {
                alert('请选择产品日期');
                return;
            }

            // 显示加载状态
            const resultsDiv = document.getElementById('results');
            resultsDiv.style.display = 'block';
            resultsDiv.innerHTML = '<div class="loading">正在查询数据...</div>';

            try {
                // 构建查询URL
                let url = `/api/production-by-date-shift?productdate=${productdate}`;
                if (workclass) {
                    url += `&workclass=${workclass}`;
                }

                const response = await fetch(url);
                const data = await response.json();

                if (data.error) {
                    resultsDiv.innerHTML = `<div class="error">查询错误: ${data.error}</div>`;
                    return;
                }

                displayResults(data);
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">网络错误: ${error.message}</div>`;
            }
        }

        function displayResults(data) {
            const resultsDiv = document.getElementById('results');
            
            // 汇总信息
            const summary = data.summary;
            const summaryHtml = `
                <div class="summary-card">
                    <h4>汇总统计</h4>
                    <p><strong>产品日期：</strong>${summary.productdate}</p>
                    <p><strong>班次筛选：</strong>${summary.workclass_filter || '全部班次'}</p>
                    <p><strong>设备数量：</strong>${summary.deviceCount}</p>
                    <p><strong>白班产量：</strong>${summary.totalDayShift}</p>
                    <p><strong>夜班产量：</strong>${summary.totalNightShift}</p>
                    <p><strong>总产量：</strong>${summary.totalProduction}</p>
                </div>
            `;

            // 设备详情表格
            let devicesHtml = '';
            if (data.devices && data.devices.length > 0) {
                devicesHtml = `
                    <table class="device-table">
                        <thead>
                            <tr>
                                <th>设备编号</th>
                                <th>设备名称</th>
                                <th>白班产量</th>
                                <th>夜班产量</th>
                                <th>总产量</th>
                                <th>记录数</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                
                data.devices.forEach(device => {
                    devicesHtml += `
                        <tr>
                            <td>${device.devno}</td>
                            <td>${device.devname}</td>
                            <td>${device.dayShift}</td>
                            <td>${device.nightShift}</td>
                            <td>${device.total}</td>
                            <td>${device.records ? device.records.length : 0}</td>
                        </tr>
                    `;
                });
                
                devicesHtml += '</tbody></table>';
            } else {
                devicesHtml = '<p>没有找到符合条件的数据</p>';
            }

            // 查询信息
            const queryInfo = data.explanation ? `
                <div class="explanation">
                    <h4>查询信息</h4>
                    <p><strong>时间范围：</strong>${data.explanation.time_range}</p>
                    <p><strong>SQL条件：</strong>${data.explanation.query_info.sql_condition}</p>
                    <p><strong>班次定义：</strong></p>
                    <ul>
                        <li>白班(B): ${data.explanation.shift_definition.B}</li>
                        <li>夜班(Y): ${data.explanation.shift_definition.Y}</li>
                    </ul>
                </div>
            ` : '';

            resultsDiv.innerHTML = summaryHtml + devicesHtml + queryInfo;
        }

        function clearResults() {
            document.getElementById('results').style.display = 'none';
        }
    </script>
</body>
</html>
